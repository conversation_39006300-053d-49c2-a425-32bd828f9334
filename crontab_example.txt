# 云服务器文件还原 Crontab 配置示例
# 
# 使用方法:
# 1. 编辑 crontab: crontab -e
# 2. 添加以下行之一到 crontab 文件中
# 3. 保存并退出

# 每天凌晨2点执行还原（谨慎使用）
# 0 2 * * * /path/to/cron.sh >> /var/log/restore_cron.log 2>&1

# 每周日凌晨3点执行还原
# 0 3 * * 0 /path/to/cron.sh >> /var/log/restore_cron.log 2>&1

# 每月1号凌晨4点执行还原
# 0 4 1 * * /path/to/cron.sh >> /var/log/restore_cron.log 2>&1

# 手动执行（不通过cron）
# 直接运行: sudo bash /path/to/cron.sh

# 注意事项:
# 1. 请将 /path/to/cron.sh 替换为实际的脚本路径
# 2. 确保脚本有执行权限: chmod +x cron.sh
# 3. 建议先手动测试脚本是否正常工作
# 4. 还原操作会覆盖现有文件，请谨慎使用
# 5. 建议在还原前备份当前重要数据

# Crontab 时间格式说明:
# 分钟(0-59) 小时(0-23) 日期(1-31) 月份(1-12) 星期(0-7, 0和7都表示星期日)
# 
# 示例:
# 0 2 * * *     每天凌晨2点
# 30 14 * * 1   每周一下午2点30分
# 0 0 1 * *     每月1号午夜
# */15 * * * *  每15分钟
