#!/bin/bash

# 云服务器文件还原脚本
# 作者: 自动生成
# 日期: $(date +%Y-%m-%d)
# 用途: 从云服务器还原备份文件

# 配置变量
BACKUP_FILE="/home/<USER>/backups/backup-Jun-26-2025-1.tar.zst"
RESTORE_DIR="/home/<USER>"
LOG_FILE="/var/log/restore_$(date +%Y%m%d_%H%M%S).log"
TEMP_DIR="/tmp/restore_temp_$(date +%Y%m%d_%H%M%S)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
error_exit() {
    echo -e "${RED}错误: $1${NC}" | tee -a "$LOG_FILE"
    cleanup
    exit 1
}

# 清理临时文件函数
cleanup() {
    log "清理临时文件..."
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
        log "临时目录已清理: $TEMP_DIR"
    fi
}

# 检查依赖
check_dependencies() {
    log "检查系统依赖..."
    
    # 检查zstd是否安装
    if ! command -v zstd &> /dev/null; then
        error_exit "zstd 未安装，请先安装: sudo apt-get install zstd 或 sudo yum install zstd"
    fi
    
    # 检查tar是否支持zstd
    if ! tar --help | grep -q zstd; then
        error_exit "tar 不支持 zstd 压缩格式"
    fi
    
    log "依赖检查完成"
}

# 检查备份文件
check_backup_file() {
    log "检查备份文件: $BACKUP_FILE"
    
    if [ ! -f "$BACKUP_FILE" ]; then
        error_exit "备份文件不存在: $BACKUP_FILE"
    fi
    
    # 检查文件大小
    file_size=$(stat -f%z "$BACKUP_FILE" 2>/dev/null || stat -c%s "$BACKUP_FILE" 2>/dev/null)
    if [ "$file_size" -eq 0 ]; then
        error_exit "备份文件为空: $BACKUP_FILE"
    fi
    
    log "备份文件检查完成，文件大小: $(du -h "$BACKUP_FILE" | cut -f1)"
}

# 创建备份
create_current_backup() {
    log "创建当前数据备份..."
    
    current_backup="/tmp/current_backup_$(date +%Y%m%d_%H%M%S).tar.zst"
    
    if [ -d "$RESTORE_DIR" ]; then
        tar --zstd -cf "$current_backup" -C "$(dirname "$RESTORE_DIR")" "$(basename "$RESTORE_DIR")" 2>/dev/null
        if [ $? -eq 0 ]; then
            log "当前数据已备份到: $current_backup"
        else
            log "警告: 无法创建当前数据备份"
        fi
    fi
}

# 还原文件
restore_files() {
    log "开始还原文件..."
    
    # 创建临时目录
    mkdir -p "$TEMP_DIR" || error_exit "无法创建临时目录: $TEMP_DIR"
    
    # 解压备份文件到临时目录
    log "解压备份文件到临时目录..."
    tar --zstd -xf "$BACKUP_FILE" -C "$TEMP_DIR" || error_exit "解压备份文件失败"
    
    # 检查解压后的内容
    log "检查解压内容..."
    extracted_content=$(ls -la "$TEMP_DIR")
    log "解压内容: $extracted_content"
    
    # 确定还原目标
    if [ -d "$TEMP_DIR/home/<USER>" ]; then
        source_dir="$TEMP_DIR/home/<USER>"
    elif [ -d "$TEMP_DIR/lankeprw" ]; then
        source_dir="$TEMP_DIR/lankeprw"
    else
        # 如果找不到预期目录，列出所有内容让用户选择
        log "未找到预期的目录结构，可用目录:"
        find "$TEMP_DIR" -type d -maxdepth 2
        error_exit "无法确定还原源目录"
    fi
    
    log "还原源目录: $source_dir"
    log "还原目标目录: $RESTORE_DIR"
    
    # 执行还原
    if [ -d "$RESTORE_DIR" ]; then
        log "目标目录已存在，将进行覆盖还原..."
        cp -rf "$source_dir"/* "$RESTORE_DIR/" || error_exit "还原文件失败"
    else
        log "创建目标目录并还原..."
        mkdir -p "$(dirname "$RESTORE_DIR")" || error_exit "无法创建父目录"
        cp -rf "$source_dir" "$(dirname "$RESTORE_DIR")/" || error_exit "还原文件失败"
    fi
    
    log "文件还原完成"
}

# 验证还原
verify_restore() {
    log "验证还原结果..."
    
    if [ -d "$RESTORE_DIR" ]; then
        file_count=$(find "$RESTORE_DIR" -type f | wc -l)
        dir_count=$(find "$RESTORE_DIR" -type d | wc -l)
        log "还原验证: 文件数量: $file_count, 目录数量: $dir_count"
        
        # 检查关键文件/目录
        key_paths=("domains" "backup" "imap")
        for path in "${key_paths[@]}"; do
            if [ -e "$RESTORE_DIR/$path" ]; then
                log "✓ 关键路径存在: $path"
            else
                log "⚠ 关键路径缺失: $path"
            fi
        done
    else
        error_exit "还原目录不存在，还原可能失败"
    fi
}

# 设置权限
set_permissions() {
    log "设置文件权限..."
    
    # 设置基本权限
    chown -R lankeprw:lankeprw "$RESTORE_DIR" 2>/dev/null || log "警告: 无法设置所有者权限"
    
    # 设置目录权限
    find "$RESTORE_DIR" -type d -exec chmod 755 {} \; 2>/dev/null || log "警告: 无法设置目录权限"
    
    # 设置文件权限
    find "$RESTORE_DIR" -type f -exec chmod 644 {} \; 2>/dev/null || log "警告: 无法设置文件权限"
    
    # 设置可执行文件权限
    find "$RESTORE_DIR" -name "*.sh" -exec chmod +x {} \; 2>/dev/null
    find "$RESTORE_DIR" -name "*.cgi" -exec chmod +x {} \; 2>/dev/null
    
    log "权限设置完成"
}

# 主函数
main() {
    echo -e "${GREEN}=== 云服务器文件还原脚本 ===${NC}"
    echo -e "${YELLOW}备份文件: $BACKUP_FILE${NC}"
    echo -e "${YELLOW}还原目录: $RESTORE_DIR${NC}"
    echo -e "${YELLOW}日志文件: $LOG_FILE${NC}"
    echo ""
    
    log "开始执行还原任务"
    
    # 检查是否以root权限运行
    if [ "$EUID" -ne 0 ]; then
        log "警告: 建议以root权限运行此脚本以确保权限设置正确"
    fi
    
    # 执行还原步骤
    check_dependencies
    check_backup_file
    create_current_backup
    restore_files
    verify_restore
    set_permissions
    cleanup
    
    log "还原任务完成"
    echo -e "${GREEN}✓ 还原完成！请检查日志文件: $LOG_FILE${NC}"
    echo -e "${GREEN}✓ 还原目录: $RESTORE_DIR${NC}"
}

# 信号处理
trap cleanup EXIT
trap 'error_exit "脚本被中断"' INT TERM

# 执行主函数
main "$@"
