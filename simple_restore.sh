#!/bin/bash

# 简化版云服务器文件还原脚本
# 备份文件路径
BACKUP_FILE="/home/<USER>/backups/backup-Jun-26-2025-1.tar.zst"
RESTORE_DIR="/home/<USER>"

echo "=== 简化版文件还原脚本 ==="
echo "备份文件: $BACKUP_FILE"
echo "还原目录: $RESTORE_DIR"
echo ""

# 检查备份文件是否存在
if [ ! -f "$BACKUP_FILE" ]; then
    echo "错误: 备份文件不存在: $BACKUP_FILE"
    exit 1
fi

# 检查zstd是否可用
if ! command -v zstd &> /dev/null; then
    echo "错误: zstd 未安装，请先安装"
    exit 1
fi

# 创建当前数据的备份（可选）
echo "创建当前数据备份..."
current_backup="/tmp/current_backup_$(date +%Y%m%d_%H%M%S).tar.zst"
if [ -d "$RESTORE_DIR" ]; then
    tar --zstd -cf "$current_backup" -C "$(dirname "$RESTORE_DIR")" "$(basename "$RESTORE_DIR")" 2>/dev/null
    echo "当前数据已备份到: $current_backup"
fi

# 还原文件
echo "开始还原文件..."
echo "解压备份文件..."

# 直接解压到目标位置
tar --zstd -xf "$BACKUP_FILE" -C / || {
    echo "错误: 解压失败"
    exit 1
}

echo "还原完成！"
echo "请检查还原的文件是否正确"

# 设置基本权限
echo "设置文件权限..."
chown -R lankeprw:lankeprw "$RESTORE_DIR" 2>/dev/null || echo "警告: 无法设置所有者权限"

echo "=== 还原任务完成 ==="
