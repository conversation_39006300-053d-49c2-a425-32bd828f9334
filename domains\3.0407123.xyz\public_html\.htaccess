# DO NOT REMOVE. CLOUDLINUX PASSENGER CONFIGURATION BEGIN
PassengerAppRoot "/home/<USER>/domains/3.0407123.xyz/public_html"
PassengerBaseURI "/"
PassengerNodejs "/home/<USER>/nodevenv/domains/3.0407123.xyz/public_html/20/bin/node"
PassengerAppType node
PassengerStartupFile index.js
# DO NOT REMOVE. CLOUDLINUX PASSENGER CONFIGURATION END
# DO NOT REMOVE OR MODIFY. CLOUDLINUX ENV VARS CONFIGURATION BEGIN
<IfModule Litespeed>
SetEnv UUID 58173a22-a287-4fb3-aadf-c11e937f76fe
</IfModule>
# DO NOT REMOVE OR MODIFY. CLOUDLINUX ENV VARS CONFIGURATION END